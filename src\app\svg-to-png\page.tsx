import React from 'react';
import { Metadata } from 'next';
import { Breadcrumb } from '@/components/Breadcrumb';
import RelatedTools from '@/components/RelatedTools';
import SvgToPngClient from './SvgToPngClient';

export const metadata: Metadata = {
  title: 'The Best Free SVG to PNG Converter - Convert SVG Files to PNG Online',
  description: 'Convert SVG files to standard PNG format online with lossless quality. Free, fast, and secure SVG to PNG converter with batch processing.',
  keywords: 'SVG to PNG, convert SVG to PNG, vector to raster, SVG converter, free PNG converter',
  openGraph: {
    title: 'The Best Free SVG to PNG Converter',
    description: 'Convert SVG files to standard PNG format online with lossless quality',
    url: 'https://heic-tojpg.com/svg-to-png',
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: 'https://image.heic-tojpg.com/svg-to-png-converter-tool.webp',
        width: 1200,
        height: 630,
        alt: 'SVG to PNG Converter Tool',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free SVG to PNG Converter',
    description: 'Convert SVG files to standard PNG format online with lossless quality',
    images: ['https://image.heic-tojpg.com/svg-to-png-converter-tool.webp'],
  },
  alternates: {
    canonical: 'https://heic-tojpg.com/svg-to-png',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function SvgToPng() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "SVG to PNG Converter",
    "description": "Convert SVG files to standard PNG format online with lossless quality",
    "url": "https://heic-tojpg.com/svg-to-png",
    "applicationCategory": "UtilitiesApplication",
    "operatingSystem": "Any",
    "permissions": "browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Free SVG to PNG conversion",
      "Batch processing",
      "High-quality output",
      "Privacy protection",
      "No registration required"
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative">
        <Breadcrumb />
        <h1 className="text-2xl md:text-4xl font-bold text-center mb-4 md:mb-8 text-gray-800">
          The Best Free SVG to PNG Converter
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Convert SVG files to standard PNG format online with lossless quality
        </p>

        <SvgToPngClient />

        <div className="mt-12 space-y-16">
          <section className="introduction">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">
              <a href="/svg-to-png" className="hover:text-indigo-600 transition-colors">SVG to PNG</a> Converter Features
            </h2>
            <div className="space-y-16">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">1</span>
                      Professional SVG to PNG Conversion - 100% Free
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>No registration or payment required - completely free tool to convert SVG to PNG with vector-to-raster rendering accuracy</li>
                      <li>Intuitive drag-and-drop interface with one-click upload functionality for efficient SVG file conversion</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">2</span>
                      High-Speed SVG to PNG Transformation with Precise Rendering
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Advanced vector-to-raster conversion algorithms preserve all SVG elements with crisp edges</li>
                      <li>Maintains full alpha channel transparency when you transform SVG to PNG with anti-aliasing technology</li>
                    </ul>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/svg-to-png-converter-tool.webp"
                    alt="Professional SVG to PNG Converter Tool"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>
            </div>
          </section>

          <RelatedTools currentTool="SVG to PNG" />
        </div>
      </main>
    </>
  );
}