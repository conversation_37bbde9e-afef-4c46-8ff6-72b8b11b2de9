import { Metadata } from 'next';
import { Breadcrumb } from '@/components/Breadcrumb';
import RelatedTools from '@/components/RelatedTools';
import WebpToAvifConverter from '@/components/WebpToAvifConverter';
import { baseUrl } from '../layout';

// 添加页面元数据用于SEO
export const metadata: Metadata = {
  title: 'The Best Free WebP to AVIF Converter',
  description: 'Convert WebP photos to modern AVIF format online',
  keywords: 'webp to avif, convert webp to avif, webp to avif converter, webp to avif free',
  openGraph: {
    title: 'The Best Free WebP to AVIF Converter',
    description: 'Convert WebP photos to modern AVIF format online',
    type: 'website',
    url: `${baseUrl}/webp-to-avif`,
  },
  alternates: {
    canonical: `${baseUrl}/webp-to-avif`,
  },
};

export default function WebpToAvif() {
  return (
    <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
      <Breadcrumb />
      <WebpToAvifConverter />
      <div className="mt-12 p-4 bg-gradient-to-br from-slate-50/90 via-white to-white rounded-lg shadow-md border border-gray-100">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">Share with Friends</h3>
        <p className="text-sm text-gray-600 mb-4">If you found this tool useful, please share it with friends who might need it!</p>
        <div className="sharethis-inline-share-buttons"></div>
      </div>
      <RelatedTools currentTool="WebP to AVIF" />
    </main>
  );
}