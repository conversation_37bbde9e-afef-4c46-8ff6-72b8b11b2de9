import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for WebP to AVIF page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free WebP to AVIF Converter',
  description: 'Convert WebP photos to modern AVIF format online',
  keywords: 'webp to avif, convert webp to avif, webp to avif converter, webp to avif free',
  alternates: {
    canonical: `${baseUrl}/webp-to-avif`,
  },
  openGraph: {
    title: 'The Best Free WebP to AVIF Converter',
    description: 'Convert WebP photos to modern AVIF format online',
    url: `${baseUrl}/webp-to-avif`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/webp-to-avif-converter.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free WebP to AVIF Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free WebP to AVIF Converter',
    description: 'Convert WebP photos to modern AVIF format online',
    images: [`https://image.heic-tojpg.com/webp-to-avif-converter.webp`],
  },
};

export default function WebpToAvifLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 